import type { Component } from 'vue';

import { defineComponent, h } from 'vue';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api/core/file';

// 高阶组件工厂，为任何组件添加上传默认值
export function withUploadDefaults<T extends Component>(
  WrappedComponent: T,
  defaults = {
    uploadApi: uploadFileApi,
    previewApi: getDownloadFileLinkApi,
  }
) {
  return defineComponent({
    name: `WithUploadDefaults(${WrappedComponent.name || 'Component'})`,
    inheritAttrs: false,
    setup(props, { attrs, slots }) {
      // 合并默认值和传入的属性
      const mergedProps = {
        ...defaults,
        ...attrs,
      };

      return () => h(WrappedComponent, mergedProps, slots);
    },
  });
}

// 使用示例
import { BaseUpload } from '@vben/base-ui';

export const SimpleUpload = withUploadDefaults(BaseUpload);
