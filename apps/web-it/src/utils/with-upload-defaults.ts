import type { Component } from 'vue';

import { defineComponent, h } from 'vue';

import { BaseUpload } from '@vben/base-ui';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api/core/file';

// 默认配置
const DEFAULT_UPLOAD_CONFIG = {
  uploadApi: uploadFileApi,
  previewApi: getDownloadFileLinkApi,
} as const;

// 高阶组件工厂，为任何组件添加上传默认值
export function withUploadDefaults<T extends Component>(
  WrappedComponent: T,
  defaults?: Record<string, any>
) {
  const finalDefaults = defaults || DEFAULT_UPLOAD_CONFIG;

  return defineComponent({
    name: `WithUploadDefaults(${WrappedComponent.name || 'Component'})`,
    inheritAttrs: false,
    props: {
      // 声明所有可能的 props，这样 v-model 才能正确工作
      uploadApi: { type: Function, required: false },
      previewApi: { type: Function, required: false },
    },
    setup(props, { attrs, slots }) {
      // 合并默认值和传入的属性，传入的属性优先级更高
      const mergedProps = {
        ...finalDefaults,
        ...attrs,
        ...props,
        // 只有在没有传入对应 API 时才使用默认值
        uploadApi: props.uploadApi || finalDefaults.uploadApi,
        previewApi: props.previewApi || finalDefaults.previewApi,
      };

      return () => h(WrappedComponent, mergedProps, slots);
    },
  });
}

// 直接创建 SimpleUpload 组件，避免复杂的高阶组件问题
export const SimpleUpload = defineComponent({
  name: 'SimpleUpload',
  inheritAttrs: false,
  setup(_, { attrs, slots }) {
    // 默认值在前，传入的属性在后，这样传入的属性会覆盖默认值
    const finalProps = {
      uploadApi: uploadFileApi,
      previewApi: getDownloadFileLinkApi,
      ...attrs, // attrs 包含了所有传入的属性，包括 v-model 绑定
    };

    return () => h(BaseUpload, finalProps, slots);
  },
});
