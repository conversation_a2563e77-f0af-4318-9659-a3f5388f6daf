import type { Component } from 'vue';

import { defineComponent, h } from 'vue';

import { BaseUpload } from '@vben/base-ui';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api';

// 默认配置
const DEFAULT_UPLOAD_CONFIG = {
  uploadApi: uploadFileApi,
  previewApi: getDownloadFileLinkApi,
} as const;

// 高阶组件工厂，为任何组件添加上传默认值
export function withUploadDefaults<T extends Component>(
  WrappedComponent: T,
  defaults?: Record<string, any>
) {
  const finalDefaults = defaults || DEFAULT_UPLOAD_CONFIG;

  return defineComponent({
    name: `WithUploadDefaults(${WrappedComponent.name || 'Component'})`,
    inheritAttrs: false,
    setup(_, { attrs, slots }) {
      // 合并默认值和传入的属性
      const mergedProps = {
        ...finalDefaults,
        ...attrs,
      };

      return () => h(WrappedComponent, mergedProps, slots);
    },
  });
}

export const SimpleUpload = withUploadDefaults(BaseUpload);
