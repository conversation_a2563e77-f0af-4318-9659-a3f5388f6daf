<script setup lang="ts">
import type { InspectionInfo } from '#/api';

import { computed, provide, reactive, ref, unref } from 'vue';

import { BaseUpload } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import dayjs from 'dayjs';

import BaseForm from '#/views/operation/inspection/manage/components/base-form.vue';
import CompanyListForm from '#/views/operation/inspection/manage/components/company-list-form.vue';
import ContractForm from '#/views/operation/inspection/manage/components/contract-form.vue';
import CoreCompanyForm from '#/views/operation/inspection/manage/components/core-company-form.vue';
import PartnerCompanyForm from '#/views/operation/inspection/manage/components/partner-company-form.vue';
import PurchaseSalesForm from '#/views/operation/inspection/manage/components/purchase-sales-form.vue';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

const emit = defineEmits(['ok', 'register']);
const colSpan = COL_SPAN_PROP;
const state = reactive<{ coreCompanTitle: string; id?: number }>({
  id: undefined,
  coreCompanTitle: '核心企业投后管理情况',
});
const pageTitle = computed(() => {
  return state.id ? '编辑' : '新增';
});
const inspectionForm = ref<Partial<InspectionInfo>>({
  inspectionDate: dayjs().startOf('day').format('x'),
  reportDate: dayjs().startOf('day').format('x'),
});
const getDetail = async () => {
  if (state.id) {
    // inspectionForm.value = await getInspectionDetailApi(state.id);
  }
};
const init = (data: { id: number }) => {
  state.id = data.id;
  getDetail();
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const BaseFormRef = ref();
const PurchaseSalesFormRef = ref();
const supplierCompanyListFormRef = ref();
const downCompanyListFormRef = ref();
const warehouseCompanyListFormRef = ref();
const save = async () => {
  const formDaa = unref(inspectionForm);
  // await BaseFormRef.value.save();
  await supplierCompanyListFormRef.value.save();
  await warehouseCompanyListFormRef.value.save();
  const { purchaseSalesData } = await PurchaseSalesFormRef.value.save();
  formDaa.purchaseSalesData = purchaseSalesData;
  changeOkLoading(true);
  try {
    // await api(unref(templateForm) as ContractTemplateInfo);
    // emit('ok');
    // closePopup();
  } finally {
    changeOkLoading(false);
  }
};

provide(inspectionFormKey, inspectionForm);
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="pageTitle" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <!-- 基本情况 -->
      <BaseForm ref="BaseFormRef" />
      <!-- 采购销售情况 -->
      <PurchaseSalesForm ref="PurchaseSalesFormRef" />
      <!-- 合同履约情况 -->
      <ContractForm />
      <!-- 合作企业投后管理情况 -->
      <PartnerCompanyForm />
      <!-- 核心企业/担保企业投后管理情况 -->
      <CoreCompanyForm :title="state.coreCompanTitle" />
      <!-- 供应商情况 -->
      <CompanyListForm ref="supplierCompanyListFormRef" company-type="supplier" />
      <!-- 下游客户情况 -->
      <CompanyListForm ref="downCompanyListFormRef" company-type="down" />
      <!-- 仓库情况 -->
      <CompanyListForm ref="warehouseCompanyListFormRef" company-type="warehouse" />
      <a-form v-bind="FORM_PROP">
        <BasicCaption content="抵质押物检查情况" class="mb-4" />
        <a-form-item label="存续情况" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.collateralStatus" :rows="4" />
        </a-form-item>
        <BasicCaption content="检查结果" class="mb-4" />
        <a-form-item label="其他情况" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.otherInfoSummary" :rows="4" />
        </a-form-item>
        <a-form-item label="检查结论" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.conclusion" :rows="4" />
        </a-form-item>
        <a-form-item label="资产分类" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.assetClassification" :rows="4" />
        </a-form-item>
        <BasicCaption content="其他信息" class="mb-4" />
        <a-row>
          <a-col v-bind="colSpan">
            <a-form-item label="运营人员">
              <a-input v-model:value="inspectionForm.operationsOfficer" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="复核人员">
              <a-input v-model:value="inspectionForm.reviewOfficer" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="报告日期">
              <a-date-picker v-model:value="inspectionForm.reportDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="底稿附件" v-bind="FULL_FORM_ITEM_PROP">
              <a-textarea v-model:value="inspectionForm.wait6" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <BasicCaption content="现场图片" class="mb-4" />
      <div>
        <BaseUpload upload-api="todo" />
      </div>
    </div>
  </BasicPopup>
</template>

<style></style>
